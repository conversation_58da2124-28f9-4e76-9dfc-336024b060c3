import { useState, useCallback, useMemo } from 'react'

import { convertBase64 } from '@/utils/common'

import { useImageManager } from './useImageManager'

/**
 * Хук для автономного управления изображениями продукта
 * Использует существующий useImageManager, но с собственным состоянием
 */
export const useProductImagesSave = (productPublicId, defaultFormData) => {
  // Локальное состояние для изображений
  const [pictures, setPictures] = useState([])
  const [mainImage, setMainImage] = useState('')

  // Используем переданные данные товара
  const productData = useMemo(() => defaultFormData || {}, [defaultFormData])

  // Инициализируем состояние изображений из данных товара
  const initializeFromProduct = useCallback(() => {
    if (productData) {
      setPictures(productData.pictures || [])
      setMainImage(productData.picture || '')
    }
  }, [productData])

  // Создаем функции для работы с локальным состоянием
  const getFieldValue = useCallback(
    (fieldName, defaultValue = '') => {
      if (fieldName === 'pictures') return pictures
      if (fieldName === 'picture') return mainImage
      return defaultValue
    },
    [pictures, mainImage]
  )

  const onChangePictures = useCallback((newPictures) => {
    setPictures(newPictures)
  }, [])

  const onUpdateMainPicture = useCallback((newMainImage) => {
    setMainImage(newMainImage)
  }, [])

  // Используем существующий useImageManager с нашими функциями
  const imageManager = useImageManager({
    defaultFormData: productData || {},
    getFieldValue,
    onChangePictures,
    onSavedPictures: onChangePictures, // Обновляем локальное состояние после сохранения
    onSavedMainPicture: onUpdateMainPicture, // Обновляем локальное состояние после сохранения
    onUpdateMainPicture,
    productPublicId,
  })

  // Обработчики для загрузки файлов
  const handleMainImageUpload = useCallback(
    async (file) => {
      if (!file) return

      if (!imageManager.validateFileSize(file)) return

      try {
        const base64 = await convertBase64(file)
        onUpdateMainPicture(base64 === '' ? '' : base64)
        imageManager.setLastSavedMainValue(null)
        imageManager.setDisableOnce(false)
      } catch {
        imageManager.openToast.error({ message: 'Ошибка при обработке файла' })
      }
    },
    [imageManager, onUpdateMainPicture]
  )

  const handleMultipleImagesUpload = useCallback(
    async (files) => {
      if (!files || files.length === 0) return

      const filesArray = Array.from(files)
      const oversizedFiles = []
      const validFiles = []

      // Разделяем файлы на валидные и слишком большие
      filesArray.forEach((file) => {
        if (imageManager.validateFileSize(file)) {
          validFiles.push(file)
        } else {
          oversizedFiles.push(file.name)
        }
      })

      try {
        // Конвертируем валидные файлы в base64
        const base64Images = await Promise.all(validFiles.map((file) => convertBase64(file)))

        // Обновляем список изображений
        onChangePictures([...pictures, ...base64Images])
        imageManager.setDisableOnce(false)
      } catch {
        imageManager.openToast.error({ message: 'Ошибка при обработке файлов' })
      }
    },
    [imageManager, pictures, onChangePictures]
  )

  const deleteImage = useCallback(
    (index) => {
      const newPictures = [...pictures]
      newPictures.splice(index, 1)
      onChangePictures(newPictures)
      imageManager.setDisableOnce(false)
    },
    [pictures, onChangePictures, imageManager]
  )

  const moveImage = useCallback(
    (index, toIndex) => {
      if (toIndex < 0 || toIndex >= pictures.length) return

      const newPictures = [...pictures]
      const [movedElement] = newPictures.splice(index, 1)
      newPictures.splice(toIndex, 0, movedElement)
      onChangePictures(newPictures)
      imageManager.setDisableOnce(false)
    },
    [pictures, onChangePictures, imageManager]
  )

  return {
    // Состояние
    pictures,
    mainImage,
    isUploading: imageManager.isUploading,
    disableOnce: imageManager.disableOnce,
    hasChanges: imageManager.hasChanges,
    productData,

    // Действия из imageManager
    saveImages: async () => {
      // Используем существующую логику сохранения
      const {
        productPublicId: pid,
        hasChanges,
        isUploading,
        setIsUploading,
        hasBase64,
        orderChanged,
        mainChanged,
        savedNormalized,
        mainCurrent,
        uploadBase64Images,
        deleteUnusedFiles,
        updateProductItemMutateAsync,
        setLastSavedMainValue,
        setDisableOnce,
        openToast,
      } = imageManager

      if (!pid) {
        openToast.error({ message: 'Сначала сохраните товар' })
        return
      }

      if (!hasChanges || isUploading) return

      setIsUploading(true)
      try {
        let updatedPictures = []

        if (hasBase64 || orderChanged) {
          updatedPictures = await uploadBase64Images(pictures)
          await deleteUnusedFiles(savedNormalized, updatedPictures)
        }

        const payload = {
          ...((hasBase64 || orderChanged) && { pictures: updatedPictures }),
          ...(mainChanged && { picture: mainCurrent }),
        }

        const response = await updateProductItemMutateAsync({
          public_id: pid,
          data: payload,
        })

        if (response?.status === 200) {
          if (hasBase64 || orderChanged) {
            setPictures(updatedPictures)
          }
          if (mainChanged) {
            setLastSavedMainValue(mainCurrent)
          }

          setDisableOnce(true)
          openToast.success({ message: 'Изображения сохранены' })
        }
      } catch (error) {
        console.error('Error saving pictures:', error)
      } finally {
        setIsUploading(false)
      }
    },

    // Обработчики
    handleMainImageUpload,
    handleMultipleImagesUpload,
    deleteImage,
    moveImage,
    initializeFromProduct,

    // Утилиты
    validateFileSize: imageManager.validateFileSize,
    setDisableOnce: imageManager.setDisableOnce,
  }
}
