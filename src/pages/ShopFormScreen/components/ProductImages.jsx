import { useRef, useCallback, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from 'react-bootstrap'

import FileUploadSection from './FileUploadSection'
import ImageGallery from './ImageGallery'
import { useProductImagesSave } from '../hooks/useProductImagesSave'

function ProductImages({ productPublicId, defaultFormData }) {
  const picturesInputRef = useRef()

  const imageManager = useProductImagesSave(productPublicId, defaultFormData)

  // Инициализируем изображения при загрузке данных товара
  useEffect(() => {
    if (imageManager.productData) {
      imageManager.initializeFromProduct()
    }
  }, [imageManager.productData]) // eslint-disable-line react-hooks/exhaustive-deps

  /**
   * Сохраняет изменения изображений
   */
  const handleSavePictures = useCallback(async () => {
    await imageManager.saveImages()

    // Очистка input после сохранения
    if (picturesInputRef.current) {
      picturesInputRef.current.value = ''
    }
  }, [imageManager, picturesInputRef])

  /**
   * Обработчик загрузки главного изображения
   */
  const handleFileRead = useCallback(
    async (event) => {
      const file = event.target.files[0]
      await imageManager.handleMainImageUpload(file)
    },
    [imageManager]
  )

  /**
   * Обработчик загрузки дополнительных изображений
   */
  const handleMorePicture = useCallback(
    async (event) => {
      const files = event.target.files
      await imageManager.handleMultipleImagesUpload(files)
    },
    [imageManager]
  )

  /**
   * Удаляет изображение из галереи по индексу
   */
  const handleDeletePictures = useCallback(
    (index) => {
      imageManager.deleteImage(index)

      // Очищаем input если больше нет изображений
      if (imageManager.pictures.length <= 1 && picturesInputRef.current) {
        picturesInputRef.current.value = ''
      }
    },
    [imageManager, picturesInputRef]
  )

  /**
   * Перемещает изображение в списке
   */
  const movePictureInList = useCallback(
    (index, toIndex) => {
      imageManager.moveImage(index, toIndex)
    },
    [imageManager]
  )

  const { pictures, mainImage, hasChanges, disableOnce, isUploading } = imageManager

  return (
    <Card className="shadow-sm mb-5">
      <Card.Header className="bg-light">
        <Row className="align-items-center">
          <Col>
            <h5 className="mb-0 text-primary">
              <i className="bi bi-images me-2"></i>
              Изображения товара
              {hasChanges && (
                <Badge bg="warning" text="dark" className="ms-2">
                  <i className="bi bi-exclamation-circle me-1"></i>
                  Есть изменения
                </Badge>
              )}
              {!productPublicId && (
                <Badge bg="secondary" className="ms-2">
                  <i className="bi bi-info-circle me-1"></i>
                  Сначала сохраните товар
                </Badge>
              )}
            </h5>
          </Col>
          <Col xs="auto">
            <Button
              variant={hasChanges ? 'primary' : 'outline-secondary'}
              size="sm"
              onClick={handleSavePictures}
              disabled={!hasChanges || disableOnce || isUploading || !productPublicId}
            >
              {isUploading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  Сохранение...
                </>
              ) : (
                <>
                  <i className="bi bi-check-circle me-2"></i>
                  Сохранить изображения
                </>
              )}
            </Button>
          </Col>
        </Row>
      </Card.Header>

      <Card.Body>
        {/* Секция загрузки файлов */}
        <FileUploadSection
          onMainImageChange={handleFileRead}
          onMultipleImagesChange={handleMorePicture}
          picturesInputRef={picturesInputRef}
          disabled={isUploading}
        />

        {/* Галерея изображений */}
        <ImageGallery
          mainImage={mainImage}
          pictures={pictures}
          onMoveImage={movePictureInList}
          onDeleteImage={handleDeletePictures}
          isEditable={true}
        />
      </Card.Body>
    </Card>
  )
}

export default ProductImages
