import { useCallback } from 'react'
import { Col, <PERSON>, <PERSON><PERSON>, <PERSON>, Badge, ButtonGroup } from 'react-bootstrap'

import { getImageSrc } from '@/utils/images'

/**
 * Компонент для отображения одного изображения с кнопками управления
 */
const ImageItem = ({ item, index, isEditable, onMoveLeft, onMoveRight, onDelete, isFirst, isLast }) => (
  <Col xs={6} md={4} lg={3} className="mb-3">
    <Card className="h-100 shadow-sm">
      <div className="position-relative">
        <Card.Img
          variant="top"
          src={getImageSrc(item)}
          alt={`Дополнительное изображение ${index + 1}`}
          style={{ height: '150px', objectFit: 'cover' }}
        />
        <Badge bg="secondary" className="position-absolute top-0 start-0 m-2">
          {index + 1}
        </Badge>
      </div>

      {isEditable && (
        <Card.Footer className="p-2">
          <ButtonGroup size="sm" className="w-100">
            <Button
              variant="outline-secondary"
              onClick={() => onMoveLeft(index)}
              disabled={isFirst}
              title="Переместить влево"
            >
              <i className="bi bi-arrow-left"></i>
            </Button>
            <Button variant="outline-danger" onClick={() => onDelete(index)} title="Удалить изображение">
              <i className="bi bi-trash"></i>
            </Button>
            <Button
              variant="outline-secondary"
              onClick={() => onMoveRight(index)}
              disabled={isLast}
              title="Переместить вправо"
            >
              <i className="bi bi-arrow-right"></i>
            </Button>
          </ButtonGroup>
        </Card.Footer>
      )}
    </Card>
  </Col>
)

/**
 * Компонент галереи изображений
 */
function ImageGallery({ mainImage, pictures = [], onMoveImage, onDeleteImage, isEditable = true }) {
  const handleMoveLeft = useCallback(
    (index) => {
      onMoveImage(index, index - 1)
    },
    [onMoveImage]
  )

  const handleMoveRight = useCallback(
    (index) => {
      onMoveImage(index, index + 1)
    },
    [onMoveImage]
  )

  return (
    (mainImage || pictures.length > 0) && (
      <Row>
        {/* Главное изображение */}
        {mainImage && (
          <Col xs={6} md={4} lg={3} className="mb-3">
            <Card className="h-100 shadow-sm">
              <div className="position-relative">
                <Card.Img
                  variant="top"
                  src={getImageSrc(mainImage)}
                  alt="Главное изображение"
                  style={{ height: '150px', objectFit: 'cover' }}
                />
                <Badge bg="primary" className="position-absolute top-0 start-0 m-2">
                  Главное
                </Badge>
              </div>
            </Card>
          </Col>
        )}

        {/* Дополнительные изображения */}
        {pictures.map((item, index) => (
          <ImageItem
            key={`picture-${index}`}
            item={item}
            index={index}
            isEditable={isEditable}
            onMoveLeft={handleMoveLeft}
            onMoveRight={handleMoveRight}
            onDelete={onDeleteImage}
            isFirst={index === 0}
            isLast={index === pictures.length - 1}
          />
        ))}
      </Row>
    )
  )
}

export default ImageGallery
