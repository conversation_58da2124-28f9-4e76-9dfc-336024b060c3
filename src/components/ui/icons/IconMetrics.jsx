import { useTheme } from '@/contexts/ThemeContext'

const IconMetrics = (props) => {
  const { effectiveTheme, themes } = useTheme()

  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" viewBox="0 0 24 24" {...props}>
      <g fill={effectiveTheme === themes.LIGHT ? 'black' : 'white'}>
        <path d="M3 3v18h18v-2H5V3H3z" />
        <path d="M7 17h2v-7H7v7zm4-10h2v10h-2V7zm4 3h2v7h-2v-7zm4-6h2v13h-2V4z" />
      </g>
    </svg>
  )
}

export default IconMetrics
