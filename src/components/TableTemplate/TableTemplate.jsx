import { useState } from 'react'
import { <PERSON>, Col, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, Modal<PERSON>eader, Row, Table } from 'react-bootstrap'
import { useNavigate } from 'react-router-dom'

import { recompose } from '@/utils/common'

import TableRow from './TableRow'
import styles from './TableTemplate.module.scss'

const TableTemplate = ({
  data,
  values,
  actionsName = 'Действие',
  actions = null,
  actionRow = null,
  minWidth = '1200',
  scrolled,
  maxWidth = '',
  minHeight = 'unset',
  renderCustomCell,
}) => {
  const [imageModal, setImageModal] = useState(null)
  const [imageModalShow, setImageModalShow] = useState(false)

  const navigate = useNavigate()

  const isTitle = data?.title?.length > 0

  const isClickable = !!(data.clickUrl && data.clickUrlParam)

  const handleClickRow = (value) => {
    if (isClickable && !actionRow) {
      const url = data.clickUrl
      const urlPath = recompose(value, data.clickUrlParam)

      navigate(`${url}/${urlPath}`)
    } else if (actionRow) {
      actionRow(value)
    }
  }

  if (!values) return null

  return (
    <Card style={{ maxWidth: maxWidth, minHeight: minHeight }} className={`${styles.cardWrapper} mb-3 border-0`}>
      {isTitle && (
        <Card.Header className="pb-0 bg-body-secondary">
          <Card.Title>{data.title}</Card.Title>
        </Card.Header>
      )}

      <Card.Body className="p-0" style={{ minWidth: `${minWidth}px` }}>
        <Table>
          <thead style={{ top: scrolled ? '0' : '68px' }}>
            <tr>
              {data.list.map((item) => (
                <th key={item.label}>{item.label}</th>
              ))}
              {actions && <th>{actionsName}</th>}
            </tr>
          </thead>
          <tbody>
            {values.map((value, index) => (
              <TableRow
                key={`${value.id || value.public_id || 'item'}-${index}`}
                value={value}
                index={index}
                data={data}
                actions={actions}
                isClickable={isClickable}
                actionRow={actionRow}
                onClickRow={handleClickRow}
                renderCustomCell={renderCustomCell}
                setImageModal={setImageModal}
                setImageModalShow={setImageModalShow}
              />
            ))}
          </tbody>
        </Table>

        <Modal
          show={imageModalShow}
          onHide={() => {
            setImageModalShow(false)
            setImageModal(null)
          }}
          fullscreen
        >
          <ModalHeader closeButton>Результаты</ModalHeader>
          <ModalBody>
            <Row className="d-flex justify-content-center">
              <Col md="auto">{imageModal && <img src={imageModal} alt="результат" />}</Col>
            </Row>
          </ModalBody>
        </Modal>
      </Card.Body>
    </Card>
  )
}

export default TableTemplate
