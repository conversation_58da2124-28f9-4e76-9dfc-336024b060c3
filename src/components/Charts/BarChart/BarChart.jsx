import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js'
import { useRef } from 'react'
import { Bar } from 'react-chartjs-2'

import { useTheme } from '@/contexts/ThemeContext'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

/**
 * Компонент бар-чарта
 * @param {Object} props - Пропсы компонента
 * @param {Object} props.data - Данные для графика
 * @param {string} props.title - Заголовок графика
 * @param {number} props.height - Высота графика
 * @param {boolean} props.showLegend - Показывать ли легенду
 * @param {boolean} props.showGrid - Показывать ли сетку
 * @param {Object} props.colors - Цвета для графика
 * @param {Function} props.formatValue - Функция форматирования значений
 */
function BarChart({
  data,
  title = 'График',
  height = 400,
  showLegend = false,
  showGrid = true,
  colors = {
    primary: '#3b82f6',
    primaryLight: 'rgba(59, 130, 246, 0.8)',
    hover: 'rgba(59, 130, 246, 0.9)',
  },
  formatValue = (value) => value,
}) {
  const chartRef = useRef(null)
  const { effectiveTheme, themes } = useTheme()
  const isDark = effectiveTheme === themes.DARK

  if (!data || !data.labels || !data.datasets) {
    return (
      <div className="bg-body rounded-3 shadow-sm border p-4 my-3">
        <div
          className="d-flex align-items-center justify-content-center bg-body-secondary rounded-2 border-2 border-dashed text-muted fw-medium"
          style={{ height: '300px', fontSize: '16px' }}
        >
          Нет данных для отображения
        </div>
      </div>
    )
  }

  const enhancedData = {
    ...data,
    datasets: data.datasets.map((dataset) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || colors.primaryLight,
      borderColor: dataset.borderColor || colors.primary,
      hoverBackgroundColor: dataset.hoverBackgroundColor || colors.hover,
      borderWidth: 0,
      borderRadius: {
        topLeft: 6,
        topRight: 6,
        bottomLeft: 0,
        bottomRight: 0,
      },
      borderSkipped: false,
      barPercentage: 0.8,
      categoryPercentage: 0.9,
    })),
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    categoryPercentage: 0.9,
    barPercentage: 0.8,
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20,
      },
    },
    plugins: {
      legend: {
        display: showLegend,
        position: 'top',
        labels: {
          color: isDark ? '#ffffff' : '#374151',
          font: {
            size: 12,
            weight: '500',
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'rect',
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        backgroundColor: isDark ? 'rgba(17, 24, 39, 0.95)' : 'rgba(255, 255, 255, 0.95)',
        titleColor: isDark ? '#ffffff' : '#111827',
        bodyColor: isDark ? '#ffffff' : '#374151',
        borderColor: isDark ? 'rgba(59, 130, 246, 0.3)' : 'rgba(0, 0, 0, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        padding: 12,
        titleFont: {
          size: 14,
          weight: '600',
        },
        bodyFont: {
          size: 13,
        },
        callbacks: {
          title: function (context) {
            return context[0].label
          },
          label: function (context) {
            const value = context.parsed.y
            return formatValue(value)
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: showGrid,
          color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: isDark ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
            weight: '500',
          },
          maxRotation: 0,
          minRotation: 0,
        },
      },
      y: {
        display: true,
        beginAtZero: true,
        grid: {
          display: showGrid,
          color: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          drawBorder: false,
        },
        ticks: {
          color: isDark ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
          },
          callback: function (value) {
            return formatValue(value)
          },
        },
      },
    },
    datasets: {
      bar: {
        barPercentage: 0.8,
        categoryPercentage: 0.9,
      },
    },
    elements: {
      bar: {
        borderRadius: 4,
      },
    },
  }

  return (
    <div className="bg-body rounded-3 shadow-sm border p-4">
      {/* Заголовок */}
      {title && (
        <div className="text-center mb-4">
          <h5 className="mb-0 text-body-emphasis">{title}</h5>
        </div>
      )}

      <div
        style={{
          height: `${height}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        <Bar ref={chartRef} data={enhancedData} options={options} />
      </div>
    </div>
  )
}

export default BarChart
