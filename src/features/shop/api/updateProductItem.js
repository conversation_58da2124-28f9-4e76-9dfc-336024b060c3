import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
// import { useToast } from '@/hooks/useToast'
import { axios } from '@/lib/axios'
// import { queryClient } from '@/lib/react-query'

/**
 * PUT /api/admin/product/item/{public_id}
 * body: { pictures: string[] }
 */
export const updateProductItem = ({ public_id, data }) => {
  return axios.put(`${APIRoute.SHOP_PRODUCT}/${public_id}`, data)
}
export const useUpdateProductItem = () => {
  // const openToast = useToast()

  return useMutation({
    mutationFn: updateProductItem,
    onSuccess: (resp) => {
      if (resp?.status === 200) {
        // queryClient.invalidateQueries(['shop', 'items'])
        // openToast.success({ message: 'Товар обновлён' })
      }
    },
  })
}
