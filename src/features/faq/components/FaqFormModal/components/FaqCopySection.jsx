import { useState } from 'react'
import { <PERSON>, Col, Floating<PERSON><PERSON><PERSON>, FormSelect, Button } from 'react-bootstrap'

import { useTheme } from '@/contexts/ThemeContext'
import { useGetEvents } from '@/features/events/api/getEvents'
import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'
import { getFaq } from '@/features/faq/api/getFaq'
import { useToast } from '@/hooks/useToast'

export const FaqCopySection = ({ onCopyFaq }) => {
  const [selectedEventType, setSelectedEventType] = useState('')
  const [selectedEvent, setSelectedEvent] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const openToast = useToast()
  const { effectiveTheme } = useTheme()

  const eventTypeListQuery = useGetEventTypeList()
  const eventTypeList = eventTypeListQuery?.data?.data?.values || []

  const eventsQuery = useGetEvents()

  // Фильтрация событий по выбранному типу
  const eventsByType =
    eventsQuery?.data?.data?.values?.filter((event) => event.event_type?.public_id === selectedEventType) || []

  const handleCopyFaq = async () => {
    if (!selectedEvent) {
      openToast.error({ message: 'Выберите событие для копирования FAQ' })
      return
    }

    setIsLoading(true)

    try {
      // Получаем FAQ выбранного события
      const response = await getFaq(selectedEvent)
      const faqData = response?.data?.values || []

      if (!faqData.length) {
        openToast.warning({ message: 'У выбранного события нет FAQ для копирования' })
        setIsLoading(false)
        return
      }

      const sourceFaq = faqData[0]
      if (sourceFaq?.values?.length) {
        const copiedValues = sourceFaq.values.map((section) => ({
          ...section,
          items: section.items.map((item) => ({
            question: item.question || '',
            answer: item.answer || '',
            anchor: item.anchor || '',
            cities: [], // Не заполняем cities при копировании
          })),
        }))

        onCopyFaq(copiedValues)
        openToast.success({ message: 'FAQ успешно скопирован!' })
      } else {
        openToast.warning({ message: 'У выбранного события нет содержимого FAQ для копирования' })
      }
    } catch (error) {
      console.error('Ошибка при копировании FAQ:', error)
      openToast.error({ message: 'Ошибка при копировании FAQ' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEventTypeChange = (e) => {
    setSelectedEventType(e.target.value)
    setSelectedEvent('')
  }

  return (
    <div
      className={`mb-4 p-3 shadow-sm rounded border ${effectiveTheme === 'dark' ? 'bg-dark text-light' : 'bg-white'}`}
    >
      <h6 className="mb-3">Заполнить из другого события</h6>
      <Row className="g-3">
        <Col md={4}>
          <FloatingLabel controlId="eventTypeSelect" label="Тип события">
            <FormSelect value={selectedEventType} onChange={handleEventTypeChange}>
              <option value="">Выберите тип события</option>
              {eventTypeList.map((eventType) => (
                <option key={eventType.public_id} value={eventType.public_id}>
                  {eventType.title}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={6}>
          <FloatingLabel controlId="eventSelect" label="Событие">
            <FormSelect
              value={selectedEvent}
              onChange={(e) => setSelectedEvent(e.target.value)}
              disabled={!selectedEventType || eventsQuery.isLoading}
            >
              <option value="">Выберите событие</option>
              {eventsByType.map((event) => (
                <option key={event.public_id} value={event.public_id}>
                  {event.title} - {event.public_id}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={2} className="d-flex align-items-end">
          <Button
            variant="outline-primary"
            onClick={handleCopyFaq}
            disabled={!selectedEvent || isLoading}
            className="w-100 h-100"
          >
            {isLoading ? 'Копирование...' : 'Копировать'}
          </Button>
        </Col>
      </Row>
    </div>
  )
}
