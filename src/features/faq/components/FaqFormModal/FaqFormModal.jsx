import { useMemo, useEffect } from 'react'
import { Button, Modal, Form, Accordion } from 'react-bootstrap'
import { useForm, useFieldArray, useWatch } from 'react-hook-form'

import CitySelectionModal from '@/components/Modal/CitySelectionModal/CitySelectionModal'

import { useCreateFaq } from '@/features/faq/api/createFaq'
import { useUpdateFaq } from '@/features/faq/api/updateFaq'
import { useFaqForm } from '@/features/faq/hooks/useFaqForm'
import { unixToMoment } from '@/utils/date'

import { FaqCopySection } from './components/FaqCopySection'
import FaqSection from './components/FaqSection'
import FaqStats from './components/FaqStats'
import { DEFAULT_FORM_VALUES } from './constants'
import { useAccordionState } from './hooks/useAccordionState'
import { useArrayManipulation } from './hooks/useArrayManipulation'

const FaqFormModal = ({ show, onHide, eventPublicId, eventCities = [], editData = null, isEditMode = false }) => {
  const createFaqMutation = useCreateFaq()
  const updateFaqMutation = useUpdateFaq()

  const {
    register,
    control,
    handleSubmit,
    reset,
    setValue,
    trigger,
    formState: { errors },
  } = useForm({
    defaultValues: DEFAULT_FORM_VALUES,
  })

  // Сброс формы с новыми данными при изменении editData
  useEffect(() => {
    if (isEditMode && editData && show) {
      reset({ values: editData.values || [] })
    } else if (!isEditMode && show) {
      reset(DEFAULT_FORM_VALUES)
    }
  }, [isEditMode, editData, show, reset])

  const { fields: sections, remove: removeSection } = useFieldArray({
    control,
    name: 'values',
  })

  const watchedValues = useWatch({ control, name: 'values', defaultValue: [] })

  const { activeSectionKey, activeItemKeys, handleSectionSelect, handleItemSelect, openNewSection, openNewItem } =
    useAccordionState()

  const { moveSectionUp, moveSectionDown, moveItemUp, moveItemDown } = useArrayManipulation(setValue, watchedValues)

  const {
    showCityModal,
    currentItemIndex,
    currentSectionIndex,
    handleAddSection,
    handleAddItem,
    handleRemoveItem,
    handleOpenCityModal,
    handleCitySelection,
    handleCloseCityModal,
  } = useFaqForm(setValue, watchedValues, openNewSection, openNewItem)

  // Подсчитываем статистику
  const stats = useMemo(() => {
    const categoriesCount = watchedValues.length
    const questionsCount = watchedValues.reduce((total, section) => {
      return total + (section.items?.length || 0)
    }, 0)
    return { categoriesCount, questionsCount }
  }, [watchedValues])

  const onSubmit = (data) => {
    const formattedData = {
      event: {
        public_id: eventPublicId,
      },
      values: data.values.map((section, index) => ({
        ...section,
        id: index,
        items: section.items.map((item) => ({
          ...item,
          section: section.sectionName,
        })),
      })),
    }

    if (isEditMode && editData) {
      updateFaqMutation.mutate(
        {
          publicId: editData.public_id,
          data: formattedData,
        },
        {
          onSuccess: () => {
            reset()
            onHide()
          },
        }
      )
    } else {
      createFaqMutation.mutate(formattedData, {
        onSuccess: () => {
          reset()
          onHide()
        },
      })
    }
  }

  const handleClose = () => {
    reset()
    onHide()
  }

  // Функция копирования FAQ из другого события
  const handleCopyFaq = (copiedValues) => {
    setValue('values', copiedValues)
  }

  const getCityNames = (cityIds) => {
    return cityIds.map((id) => {
      const city = eventCities.find((city) => city.public_id === id)
      if (city) {
        return `${city.city.name_ru} — ${unixToMoment(city.start_time).tz(city.timezone).format('DD.MM.YYYY')}`
      }
      return `ID: ${id}`
    })
  }

  return (
    <>
      <Modal show={show} onHide={handleClose} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>{isEditMode ? 'Редактирование ЧаВо' : 'Создание ЧаВо'}</Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit(onSubmit)}>
          <Modal.Body className="bg-body-tertiary">
            <div className="mb-4">
              <FaqCopySection onCopyFaq={handleCopyFaq} />

              <FaqStats stats={stats} />

              <Accordion activeKey={activeSectionKey} onSelect={handleSectionSelect}>
                {sections.map((section, sectionIndex) => (
                  <FaqSection
                    key={section.id}
                    section={section}
                    sectionIndex={sectionIndex}
                    sectionsLength={sections.length}
                    watchedValues={watchedValues}
                    register={register}
                    control={control}
                    errors={errors}
                    trigger={trigger}
                    activeItemKeys={activeItemKeys}
                    onItemSelect={handleItemSelect}
                    onMoveUp={moveSectionUp}
                    onMoveDown={moveSectionDown}
                    onRemove={removeSection}
                    onAddItem={handleAddItem}
                    onRemoveItem={handleRemoveItem}
                    onMoveItemUp={moveItemUp}
                    onMoveItemDown={moveItemDown}
                    onOpenCityModal={handleOpenCityModal}
                    getCityNames={getCityNames}
                  />
                ))}
              </Accordion>

              <div className="d-grid mt-4">
                <Button variant="outline-primary" type="button" onClick={() => handleAddSection(sections.length)}>
                  + Добавить категорию
                </Button>
              </div>
            </div>
          </Modal.Body>
          <Modal.Footer className="bg-body-tertiary border-top">
            <div className="d-flex gap-2 ms-auto">
              <Button variant="outline-secondary" onClick={handleClose}>
                Отмена
              </Button>
              <Button
                variant="success"
                type="submit"
                disabled={isEditMode ? updateFaqMutation.isLoading : createFaqMutation.isLoading}
              >
                {isEditMode
                  ? updateFaqMutation.isLoading
                    ? 'Сохранение...'
                    : 'Сохранить'
                  : createFaqMutation.isLoading
                    ? 'Создание...'
                    : 'Создать'}
              </Button>
            </div>
          </Modal.Footer>
        </Form>
      </Modal>

      <CitySelectionModal
        show={showCityModal}
        onHide={handleCloseCityModal}
        cities={eventCities}
        selectedCities={
          currentSectionIndex !== null && currentItemIndex !== null
            ? watchedValues[currentSectionIndex]?.items?.[currentItemIndex]?.cities || []
            : []
        }
        onConfirm={handleCitySelection}
      />
    </>
  )
}

export default FaqFormModal
