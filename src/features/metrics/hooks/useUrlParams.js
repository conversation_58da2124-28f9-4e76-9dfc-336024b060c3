import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'

/**
 * Хук для работы с URL параметрами
 * @returns {Object} Объект с функциями для работы с URL параметрами
 */
export const useUrlParams = () => {
  const [searchParams, setSearchParams] = useSearchParams()

  /**
   * Получает значение параметра из URL
   * @param {string} key - Ключ параметра
   * @param {string} defaultValue - Значение по умолчанию
   * @returns {string} Значение параметра
   */
  const getParam = useCallback(
    (key, defaultValue = '') => {
      return searchParams.get(key) || defaultValue
    },
    [searchParams]
  )

  /**
   * Устанавливает параметр в URL
   * @param {string} key - Ключ параметра
   * @param {string} value - Значение параметра
   */
  const setParam = useCallback(
    (key, value) => {
      const newParams = new URLSearchParams(searchParams)
      if (value) {
        newParams.set(key, value)
      } else {
        newParams.delete(key)
      }
      setSearchParams(newParams)
    },
    [searchParams, setSearchParams]
  )

  /**
   * Устанавливает несколько параметров одновременно
   * @param {Object} params - Объект с параметрами
   */
  const setParams = useCallback(
    (params) => {
      const newParams = new URLSearchParams(searchParams)
      Object.entries(params).forEach(([key, value]) => {
        if (value) {
          newParams.set(key, value)
        } else {
          newParams.delete(key)
        }
      })
      setSearchParams(newParams)
    },
    [searchParams, setSearchParams]
  )

  return {
    getParam,
    setParam,
    setParams,
  }
}
