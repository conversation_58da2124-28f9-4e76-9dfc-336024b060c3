import { Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'

import RetentionTab from './components/RetentionTab/RetentionTab'
import RevenueTab from './components/RevenueTab/RevenueTab'
import { useUrlParams } from './hooks/useUrlParams'

/**
 * Экран метрик и аналитики
 * Доступен только для пользователей с ролью superadmin
 */
const MetricsScreen = () => {
  const { getParam, setParam } = useUrlParams()
  const activeTab = getParam('tab', 'revenue')

  /**
   * Обработчик смены вкладки
   * @param {string} tab - Ключ вкладки
   */
  const handleTabSelect = (tab) => {
    setParam('tab', tab)
  }

  return (
    <Layout title="Метрики">
      <Tabs activeKey={activeTab} onSelect={handleTabSelect} className="mb-4">
        <Tab eventKey="revenue" title="Выручка за год">
          <RevenueTab />
        </Tab>
        <Tab eventKey="retention" title="Retention">
          <RetentionTab />
        </Tab>
      </Tabs>
    </Layout>
  )
}

export default MetricsScreen
