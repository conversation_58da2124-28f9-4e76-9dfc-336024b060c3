import { Tab, Tabs } from 'react-bootstrap'

import Layout from '@/components/Layout/Layout'

import RetentionTab from './components/RetentionTab/RetentionTab'
import RevenueTab from './components/RevenueTab/RevenueTab'

/**
 * Экран метрик и аналитики
 * Доступен только для пользователей с ролью superadmin
 */
const MetricsScreen = () => {
  return (
    <Layout title="Метрики">
      <Tabs defaultActiveKey="revenue" className="mb-4">
        <Tab eventKey="revenue" title="Выручка за год">
          <RevenueTab />
        </Tab>
        <Tab eventKey="retention" title="Retention">
          <RetentionTab />
        </Tab>
      </Tabs>
    </Layout>
  )
}

export default MetricsScreen
