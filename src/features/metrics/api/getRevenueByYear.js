import { useQuery } from 'react-query'

import { APIRoute, MILLISECONDS_IN_SECOND, SECONDS_IN_MINUTE } from '@/const'
import { axios } from '@/lib/axios'

const MINUTES = 5

/**
 * Получает данные о выручке за указанный год
 * @param {number} year - Год для получения данных
 * @returns {Promise} Промис с данными о выручке по месяцам
 */
const getRevenueByYear = (year) => {
  return axios.get(`${APIRoute.ANALYTICS_REVENUE_YEAR}/${year}`)
}

/**
 * Хук для получения данных о выручке за год
 * @param {number} year - Год для получения данных
 * @returns {Object} Объект с данными запроса
 */
export const useGetRevenueByYear = (year) => {
  return useQuery({
    queryKey: ['revenueByYear', year],
    queryFn: () => getRevenueByYear(year),
    staleTime: MINUTES * SECONDS_IN_MINUTE * MILLISECONDS_IN_SECOND,
    enabled: !!year,
  })
}
