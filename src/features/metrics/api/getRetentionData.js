import { useMutation } from 'react-query'

import { APIRoute } from '@/const'
import { axios } from '@/lib/axios'

/**
 * Получает данные о retention пользователей
 * @param {Object} params - Параметры запроса
 * @param {string} params.start_date - Дата начала периода
 * @param {string} params.end_date - Дата окончания периода
 * @param {string|null} params.event_type_public_id - ID типа события или null для всех типов
 * @returns {Promise} Промис с данными о retention
 */
const getRetentionData = (params) => {
  return axios.post(APIRoute.ANALYTICS_RETENTION, params)
}

/**
 * Хук для получения данных о retention пользователей
 * @returns {Object} Объект мутации для получения данных retention
 */
export const useGetRetentionData = () => {
  return useMutation({
    mutationFn: getRetentionData,
  })
}
