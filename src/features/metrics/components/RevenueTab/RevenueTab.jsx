import { useState, useEffect } from 'react'
import { Card, Col, Form, Row, Table, Alert } from 'react-bootstrap'

import { BarChart } from '@/components/Charts'
import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { useGetRevenueByYear } from '../../api'
import { useUrlParams } from '../../hooks/useUrlParams'

/**
 * Компонент для отображения выручки за год
 */
const RevenueTab = () => {
  const currentYear = new Date().getFullYear()
  const { getParam, setParam } = useUrlParams()
  const [selectedYear, setSelectedYear] = useState(() => {
    const yearFromUrl = getParam('year')
    return yearFromUrl ? Number(yearFromUrl) : currentYear
  })

  // Синхронизируем URL при изменении года
  useEffect(() => {
    setParam('year', selectedYear.toString())
  }, [selectedYear, setParam])

  const { data: revenueResponse, isLoading, error } = useGetRevenueByYear(selectedYear)
  const revenueData = revenueResponse?.data

  // Генерируем список годов (текущий год и 4 года назад)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  /**
   * Форматирует число как валюту
   * @param {number} amount - Сумма для форматирования
   * @returns {string} Отформатированная строка
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  /**
   * Подготавливает данные для бар-чарта
   * @returns {Object|null} Данные для графика
   */
  const getChartData = () => {
    if (!revenueData?.months) return null

    return {
      labels: revenueData.months.map((month) => month.name),
      datasets: [
        {
          label: 'Выручка',
          data: revenueData.months.map((month) => month.revenue),
          backgroundColor: 'rgba(59, 130, 246, 0.8)',
          borderColor: '#3b82f6',
          hoverBackgroundColor: 'rgba(59, 130, 246, 0.9)',
        },
      ],
    }
  }

  return (
    <div>
      <Row className="mb-4">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Выберите год</Form.Label>
            <Form.Select value={selectedYear} onChange={(e) => setSelectedYear(Number(e.target.value))}>
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>
      </Row>

      <PagePreloader isLoading={isLoading}>
        {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

        {revenueData && revenueData.total_revenue > 0 && (
          <>
            {/* График выручки */}
            <Row className="mb-4">
              <Col>
                <BarChart
                  data={getChartData()}
                  title={`Выручка по месяцам за ${selectedYear} год`}
                  height={400}
                  formatValue={formatCurrency}
                  colors={{
                    primary: '#3b82f6',
                    primaryLight: 'rgba(59, 130, 246, 0.8)',
                    hover: 'rgba(59, 130, 246, 0.9)',
                  }}
                />
              </Col>
            </Row>

            {/* Таблица и карточка с общей выручкой */}
            <Row>
              <Col lg={8}>
                <Card>
                  <Card.Header>
                    <h5 className="mb-0">Детализация по месяцам</h5>
                  </Card.Header>
                  <Card.Body>
                    <Table responsive striped hover>
                      <thead>
                        <tr>
                          <th>Месяц</th>
                          <th className="text-end">Выручка</th>
                        </tr>
                      </thead>
                      <tbody>
                        {revenueData.months?.map((month, index) => (
                          <tr key={index}>
                            <td>{month.name}</td>
                            <td className="text-end">{formatCurrency(month.revenue)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              </Col>
              <Col lg={4}>
                <Card className="bg-primary text-white">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 className="text-white-50 mb-1">Общая выручка</h6>
                        <h3 className="mb-0">{formatCurrency(revenueData.total_revenue)}</h3>
                      </div>
                      <div className="fs-1 opacity-50">
                        <i className="bi bi-graph-up" />
                      </div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </>
        )}

        {revenueData && revenueData.total_revenue === 0 && (
          <Alert variant="info" className="text-center">
            <h5>Нет данных о выручке за {selectedYear} год</h5>
            <p className="mb-3">За выбранный год отсутствуют данные о выручке. Попробуйте выбрать другой год.</p>
          </Alert>
        )}
      </PagePreloader>
    </div>
  )
}

export default RevenueTab
