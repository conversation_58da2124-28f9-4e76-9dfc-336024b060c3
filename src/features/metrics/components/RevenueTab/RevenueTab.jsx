import { useState } from 'react'
import { Card, Col, Form, Row, Table, Alert } from 'react-bootstrap'

import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { useGetRevenueByYear } from '../../api'

/**
 * Компонент для отображения выручки за год
 */
const RevenueTab = () => {
  const currentYear = new Date().getFullYear()
  const [selectedYear, setSelectedYear] = useState(currentYear)

  const { data: revenueResponse, isLoading, error } = useGetRevenueByYear(selectedYear)
  const revenueData = revenueResponse?.data

  // Генерируем список годов (текущий год и 4 года назад)
  const years = Array.from({ length: 5 }, (_, i) => currentYear - i)

  /**
   * Форматирует число как валюту
   * @param {number} amount - Сумма для форматирования
   * @returns {string} Отформатированная строка
   */
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 2,
    }).format(amount)
  }

  return (
    <div>
      <Row className="mb-4">
        <Col md={4}>
          <Form.Group>
            <Form.Label>Выберите год</Form.Label>
            <Form.Select value={selectedYear} onChange={(e) => setSelectedYear(Number(e.target.value))}>
              {years.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        </Col>
      </Row>

      <PagePreloader isLoading={isLoading}>
        {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

        {revenueData && (
          <Row>
            <Col lg={8}>
              <Card>
                <Card.Header>
                  <h5 className="mb-0">Выручка по месяцам за {selectedYear} год</h5>
                </Card.Header>
                <Card.Body>
                  <Table responsive striped hover>
                    <thead>
                      <tr>
                        <th>Месяц</th>
                        <th className="text-end">Выручка</th>
                      </tr>
                    </thead>
                    <tbody>
                      {revenueData.months?.map((month, index) => (
                        <tr key={index}>
                          <td>{month.name}</td>
                          <td className="text-end">{formatCurrency(month.revenue)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
            <Col lg={4}>
              <Card className="bg-primary text-white">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-white-50 mb-1">Общая выручка</h6>
                      <h3 className="mb-0">{formatCurrency(revenueData.total_revenue)}</h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-graph-up" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </PagePreloader>
    </div>
  )
}

export default RevenueTab
