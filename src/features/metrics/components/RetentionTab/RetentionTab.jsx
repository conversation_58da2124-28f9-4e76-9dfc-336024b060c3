import { useState, useEffect } from 'react'
import { Card, Col, Form, Row, Button, Alert } from 'react-bootstrap'

import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'

import { useGetRetentionData } from '../../api'

/**
 * Компонент для отображения данных retention
 */
const RetentionTab = () => {
  const currentDate = new Date()
  const oneYearAgo = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate())

  const [formData, setFormData] = useState({
    start_date: oneYearAgo.toISOString().split('T')[0],
    end_date: currentDate.toISOString().split('T')[0],
    event_type_public_id: null,
  })

  const { data: eventTypesResponse } = useGetEventTypeList()
  const eventTypes = eventTypesResponse?.data?.values || []

  const { mutate: getRetentionData, data: retentionResponse, isLoading, error } = useGetRetentionData()
  const retentionData = retentionResponse?.data

  // Загружаем данные при первом рендере
  useEffect(() => {
    const requestData = {
      start_date: new Date(formData.start_date).toISOString(),
      end_date: new Date(formData.end_date).toISOString(),
      event_type_public_id: formData.event_type_public_id,
    }
    getRetentionData(requestData)
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  /**
   * Обработчик изменения полей формы
   * @param {Event} e - Событие изменения
   */
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value === '' ? null : value,
    }))
  }

  /**
   * Обработчик отправки формы
   */
  const handleSubmit = () => {
    const requestData = {
      start_date: new Date(formData.start_date).toISOString(),
      end_date: new Date(formData.end_date).toISOString(),
      event_type_public_id: formData.event_type_public_id,
    }
    getRetentionData(requestData)
  }

  /**
   * Вычисляет retention rate
   * @param {number} repeatUsers - Количество повторных пользователей
   * @param {number} firstTimeUsers - Количество новых пользователей
   * @returns {string} Retention rate в процентах
   */
  const calculateRetentionRate = (repeatUsers, firstTimeUsers) => {
    if (firstTimeUsers === 0) return '0.00'
    return ((repeatUsers / firstTimeUsers) * 100).toFixed(2)
  }

  return (
    <div>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Параметры анализа</h5>
        </Card.Header>
        <Card.Body>
          <Row className="g-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label>Дата начала</Form.Label>
                <Form.Control type="date" name="start_date" value={formData.start_date} onChange={handleInputChange} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Дата окончания</Form.Label>
                <Form.Control type="date" name="end_date" value={formData.end_date} onChange={handleInputChange} />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Тип события</Form.Label>
                <Form.Select
                  name="event_type_public_id"
                  value={formData.event_type_public_id || ''}
                  onChange={handleInputChange}
                >
                  <option value="">Все типы</option>
                  {eventTypes.map((eventType) => (
                    <option key={eventType.public_id} value={eventType.public_id}>
                      {eventType.title}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col>
              <Button variant="primary" onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? 'Загрузка...' : 'Получить данные'}
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <PagePreloader isLoading={isLoading}>
        {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

        {retentionData && (
          <Row className="g-4">
            <Col md={4}>
              <Card className="bg-info text-white">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-white-50 mb-1">Одна покупка</h6>
                      <h3 className="mb-0">{retentionData.first_time_users}</h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-person-plus" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="bg-success text-white">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-white-50 mb-1">Повторные покупки</h6>
                      <h3 className="mb-0">{retentionData.repeat_users}</h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-arrow-repeat" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="bg-warning text-dark">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-muted mb-1">Retention</h6>
                      <h3 className="mb-0">
                        {calculateRetentionRate(retentionData.repeat_users, retentionData.first_time_users)}%
                      </h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-percent" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}
      </PagePreloader>
    </div>
  )
}

export default RetentionTab
