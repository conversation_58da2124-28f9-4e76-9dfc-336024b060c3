import { useState, useMemo } from 'react'
import { Card, Col, Form, Row, But<PERSON>, Al<PERSON>, ButtonGroup } from 'react-bootstrap'

import PagePreloader from '@/components/PagePreloader/PagePreloader'

import { useGetEventTypeList } from '@/features/events/api/getEventTypeList'

import { useGetRetentionData } from '../../api'

// Пресеты периодов
const DATE_PRESETS = {
  LAST_7_DAYS: 'last_7_days',
  LAST_30_DAYS: 'last_30_days',
  YEAR_TO_DATE: 'year_to_date',
  LAST_12_MONTHS: 'last_12_months',
}

const PRESET_LABELS = {
  [DATE_PRESETS.LAST_7_DAYS]: 'Последние 7 дней',
  [DATE_PRESETS.LAST_30_DAYS]: 'Последние 30 дней',
  [DATE_PRESETS.YEAR_TO_DATE]: 'С начала года',
  [DATE_PRESETS.LAST_12_MONTHS]: 'Последние 12 мес.',
}

/**
 * Компонент для отображения данных retention
 */
const RetentionTab = () => {
  const currentDate = new Date()
  const oneYearAgo = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate())

  const [formData, setFormData] = useState({
    start_date: oneYearAgo.toISOString().split('T')[0],
    end_date: currentDate.toISOString().split('T')[0],
    event_type_public_id: null,
  })
  const [selectedPreset, setSelectedPreset] = useState(null)

  const { data: eventTypesResponse } = useGetEventTypeList()
  const eventTypes = eventTypesResponse?.data?.values || []

  const { mutate: getRetentionData, data: retentionResponse, isLoading, error } = useGetRetentionData()
  const retentionData = retentionResponse?.data

  // Получаем сегодняшнюю дату в формате YYYY-MM-DD
  const today = useMemo(() => new Date().toISOString().split('T')[0], [])

  // Валидация дат
  const isValidDateRange = useMemo(() => {
    const startDate = new Date(formData.start_date)
    const endDate = new Date(formData.end_date)
    const todayDate = new Date(today)

    return startDate <= endDate && endDate <= todayDate
  }, [formData.start_date, formData.end_date, today])

  /**
   * Применяет пресет дат
   * @param {string} preset - Тип пресета
   */
  const applyDatePreset = (preset) => {
    const now = new Date()
    let startDate, endDate

    switch (preset) {
      case DATE_PRESETS.LAST_7_DAYS:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        endDate = now
        break
      case DATE_PRESETS.LAST_30_DAYS:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        endDate = now
        break
      case DATE_PRESETS.YEAR_TO_DATE:
        startDate = new Date(now.getFullYear(), 0, 1)
        endDate = now
        break
      case DATE_PRESETS.LAST_12_MONTHS:
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
        endDate = now
        break
      default:
        return
    }

    setFormData((prev) => ({
      ...prev,
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
    }))
    setSelectedPreset(preset)
  }

  /**
   * Форматирует число с разделителями
   * @param {number} number - Число для форматирования
   * @returns {string} Отформатированное число
   */
  const formatNumber = (number) => {
    return number.toLocaleString('ru-RU')
  }

  /**
   * Форматирует процент
   * @param {number} value - Значение для форматирования
   * @returns {string} Отформатированный процент
   */
  const formatPercent = (value) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'percent',
      maximumFractionDigits: 2,
    }).format(value / 100)
  }

  /**
   * Обработчик изменения полей формы
   * @param {Event} e - Событие изменения
   */
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value === '' ? null : value,
    }))

    // Сбрасываем выбранный пресет при ручном изменении дат
    if (name === 'start_date' || name === 'end_date') {
      setSelectedPreset(null)
    }
  }

  /**
   * Сброс фильтров к значениям по умолчанию
   */
  const resetFilters = () => {
    const currentDate = new Date()
    const oneYearAgo = new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), currentDate.getDate())

    setFormData({
      start_date: oneYearAgo.toISOString().split('T')[0],
      end_date: currentDate.toISOString().split('T')[0],
      event_type_public_id: null,
    })
    setSelectedPreset(null)
  }

  /**
   * Обработчик отправки формы
   */
  const handleSubmit = () => {
    if (!isValidDateRange) return

    const requestData = {
      start_date: new Date(formData.start_date).toISOString(),
      end_date: new Date(formData.end_date).toISOString(),
      event_type_public_id: formData.event_type_public_id,
    }
    getRetentionData(requestData)
  }

  /**
   * Вычисляет retention rate
   * @param {number} repeatUsers - Количество повторных пользователей
   * @param {number} firstTimeUsers - Количество новых пользователей
   * @returns {number} Retention rate в процентах
   */
  const calculateRetentionRate = (repeatUsers, firstTimeUsers) => {
    if (firstTimeUsers === 0) return 0
    return (repeatUsers / firstTimeUsers) * 100
  }

  return (
    <div>
      <Card className="mb-4">
        <Card.Header>
          <h5 className="mb-0">Параметры анализа</h5>
        </Card.Header>
        <Card.Body>
          {/* Пресеты периодов */}
          <Row className="mb-3">
            <Col>
              <Form.Label className="mb-2">Быстрый выбор периода:</Form.Label>
              <ButtonGroup className="d-flex flex-wrap gap-2">
                {Object.entries(PRESET_LABELS).map(([preset, label]) => (
                  <Button
                    key={preset}
                    variant={selectedPreset === preset ? 'primary' : 'outline-primary'}
                    size="sm"
                    onClick={() => applyDatePreset(preset)}
                  >
                    {label}
                  </Button>
                ))}
              </ButtonGroup>
            </Col>
          </Row>

          <Row className="g-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label>Дата начала</Form.Label>
                <Form.Control
                  type="date"
                  name="start_date"
                  value={formData.start_date}
                  max={today}
                  onChange={handleInputChange}
                  isInvalid={!isValidDateRange}
                />
                <Form.Control.Feedback type="invalid">
                  Дата начала не может быть больше даты окончания или будущей даты
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Дата окончания</Form.Label>
                <Form.Control
                  type="date"
                  name="end_date"
                  value={formData.end_date}
                  min={formData.start_date}
                  max={today}
                  onChange={handleInputChange}
                  isInvalid={!isValidDateRange}
                />
                <Form.Control.Feedback type="invalid">
                  Дата окончания не может быть меньше даты начала или будущей даты
                </Form.Control.Feedback>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Тип события</Form.Label>
                <Form.Select
                  name="event_type_public_id"
                  value={formData.event_type_public_id || ''}
                  onChange={handleInputChange}
                >
                  <option value="">Все типы</option>
                  {eventTypes.map((eventType) => (
                    <option key={eventType.public_id} value={eventType.public_id}>
                      {eventType.title}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
          <Row className="mt-3">
            <Col>
              <Button
                variant="primary"
                onClick={handleSubmit}
                disabled={isLoading || !isValidDateRange}
                className="me-2"
              >
                {isLoading && <span className="spinner-border spinner-border-sm me-2" />}
                {isLoading ? 'Загрузка...' : 'Получить данные'}
              </Button>
              {retentionData && (
                <Button variant="outline-secondary" onClick={resetFilters}>
                  Сбросить фильтры
                </Button>
              )}
            </Col>
          </Row>
        </Card.Body>
      </Card>

      <PagePreloader isLoading={isLoading}>
        {error && <Alert variant="danger">Ошибка при загрузке данных: {error.message}</Alert>}

        {retentionData && retentionData.total_users > 0 && (
          <Row className="g-4">
            <Col md={4}>
              <Card className="bg-info text-white">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-white-50 mb-1">Одна покупка</h6>
                      <h3 className="mb-0">{formatNumber(retentionData.first_time_users)}</h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-person-plus" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="bg-success text-white">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-white-50 mb-1">Повторные покупки</h6>
                      <h3 className="mb-0">{formatNumber(retentionData.repeat_users)}</h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-arrow-repeat" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              <Card className="bg-warning text-dark">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 className="text-muted mb-1">Retention</h6>
                      <h3 className="mb-0">
                        {formatPercent(
                          calculateRetentionRate(retentionData.repeat_users, retentionData.first_time_users)
                        )}
                      </h3>
                    </div>
                    <div className="fs-1 opacity-50">
                      <i className="bi bi-percent" />
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        )}

        {retentionData && retentionData.total_users === 0 && (
          <Alert variant="info" className="text-center">
            <h5>Нет данных за выбранный период</h5>
            <p className="mb-3">Попробуйте изменить период или тип события для получения результатов.</p>
            <Button variant="outline-primary" onClick={resetFilters}>
              Сбросить фильтры
            </Button>
          </Alert>
        )}
      </PagePreloader>
    </div>
  )
}

export default RetentionTab
