import { useEffect, useState } from 'react'
import { Button, Col, FloatingLabel, Form, FormControl, FormSelect, Modal, Row } from 'react-bootstrap'
import { useForm } from 'react-hook-form'

import { useCreateProcessing } from '@/features/processing/api/createProcessing'
import { useUpdateProcessing } from '@/features/processing/api/updateProcessing'
import { taxmode, vat } from '@/pages/CreateOrderScreen/createOrderData'
import { removeEmptyString } from '@/utils/common'

const ModalForm = ({ selectedItem, isView, onClose }) => {
  const [showPassword, setShowPassword] = useState(false)
  const {
    register,
    handleSubmit,
    formState: { errors, touchedFields },
  } = useForm({
    defaultValues: {
      entity: selectedItem?.entity,
      commission: selectedItem?.commission,
      shop_id: selectedItem?.shop_id,
      taxmode: selectedItem?.taxmode,
      module: selectedItem?.module || 'uniteller',
      password: selectedItem?.password,
      vat: {
        default: selectedItem?.vat?.default,
        ticket: selectedItem?.vat?.ticket,
        product: selectedItem?.vat?.product,
        insurance: selectedItem?.vat?.insurance,
      },
      split: selectedItem?.split,
    },
  })

  const isEdit = Boolean(selectedItem)

  const createProcessingMutation = useCreateProcessing()
  const updateProcessingMutation = useUpdateProcessing()

  function getTouchedData(data, touchedFields) {
    if (data == null || touchedFields == null) return {}

    const result = {}

    for (const key in touchedFields) {
      if (Object.prototype.hasOwnProperty.call(touchedFields, key)) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = data[key]
        }
      }
    }

    return result
  }

  const handleSubmitForm = (data) => {
    const filteredData = removeEmptyString(data)
    filteredData.vat = Object.fromEntries(Object.entries(filteredData.vat).filter(([, value]) => !Number.isNaN(value)))

    if (isEdit) {
      const touchedData = getTouchedData(filteredData, touchedFields)

      updateProcessingMutation.mutate(
        { publicId: selectedItem.public_id, data: touchedData },
        {
          onSuccess: () => {
            onClose()
          },
        }
      )
    } else {
      createProcessingMutation.mutate(filteredData, {
        onSuccess: () => {
          onClose()
        },
      })
    }
  }

  return (
    <Form onSubmit={handleSubmit(handleSubmitForm)} id="processingForm">
      <Row className="g-3 mb-3">
        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="entityLabel" label="Название точки продаж">
              <FormControl
                type="text"
                {...register('entity', { required: true, disabled: isView })}
                isInvalid={errors.entity}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="commissionLabel" label="комиссия в %">
              <FormControl
                type="number"
                min={0}
                step="0.01"
                {...register('commission', { valueAsNumber: true, required: true, disabled: isView })}
                isInvalid={errors.commission}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="shopIdLabel" label="ID точки продаж">
              <FormControl
                type="text"
                {...register('shop_id', { required: true, disabled: isView })}
                isInvalid={errors.shop_id}
                placeholder=""
              />
            </FloatingLabel>
          </Form.Group>
        </Col>
        <Col md={12}>
          <FloatingLabel controlId="taxmodeLabel" label="Система налогооблажения">
            <FormSelect
              {...register('taxmode', { valueAsNumber: true, required: true, disabled: isView })}
              isInvalid={errors.taxmode}
              aria-label="taxmode"
            >
              <option value="">выберите один из вариантов</option>
              {taxmode?.map((item) => (
                <option value={item.code} key={item.code}>
                  {item.desc}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={12}>
          <FloatingLabel controlId="moduleLabel" label="Модуль">
            <FormSelect
              {...register('module', { required: true, disabled: isView })}
              isInvalid={errors.module}
              aria-label="module"
            >
              <option value="uniteller">uniteller</option>
              <option value="robokassa">robokassa</option>
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={12}>
          <Form.Group>
            <FloatingLabel controlId="passwordLabel" label="Пароль">
              <FormControl
                type={showPassword ? 'text' : 'password'}
                {...register('password', { required: false, disabled: isView })}
                isInvalid={errors.password}
                placeholder=""
              />
            </FloatingLabel>
            <div className="mt-1 d-flex justify-content-end">
              <Button variant="link" size="sm" className="p-0" onClick={() => setShowPassword(!showPassword)}>
                {showPassword ? 'Скрыть пароль' : 'Показать пароль'}
              </Button>
            </div>
          </Form.Group>
        </Col>
      </Row>

      <h5>НДС</h5>
      <Row className="g-3 mb-3">
        <Col md={12}>
          <FloatingLabel controlId="vatDefaultLabel" label="По умолчанию">
            <FormSelect
              {...register('vat.default', { valueAsNumber: true, required: true, disabled: isView })}
              isInvalid={errors?.vat?.default}
              aria-label="default"
            >
              <option value="">выберите один из вариантов</option>
              {vat?.map((item) => (
                <option value={item.code} key={item.code}>
                  {item.desc}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={12}>
          <FloatingLabel controlId="vatTicketLabel" label="Билеты">
            <FormSelect
              {...register('vat.ticket', { valueAsNumber: true, disabled: isView })}
              isInvalid={errors?.vat?.ticket}
              aria-label="ticket"
            >
              <option value="">выберите один из вариантов</option>
              {vat?.map((item) => (
                <option value={item.code} key={item.code}>
                  {item.desc}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={12}>
          <FloatingLabel controlId="vatProductLabel" label="Товары">
            <FormSelect
              {...register('vat.product', { valueAsNumber: true, disabled: isView })}
              isInvalid={errors?.vat?.product}
              aria-label="product"
            >
              <option value="">выберите один из вариантов</option>
              {vat?.map((item) => (
                <option value={item.code} key={item.code}>
                  {item.desc}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
        <Col md={12}>
          <FloatingLabel controlId="vatInsuranceLabel" label="Страховки">
            <FormSelect
              {...register('vat.insurance', { valueAsNumber: true, disabled: isView })}
              isInvalid={errors?.vat?.insurance}
              aria-label="insurance"
            >
              <option value="">выберите один из вариантов</option>
              {vat?.map((item) => (
                <option value={item.code} key={item.code}>
                  {item.desc}
                </option>
              ))}
            </FormSelect>
          </FloatingLabel>
        </Col>
      </Row>

      <Row>
        <Col>
          <Form.Check
            {...register('split', { disabled: isView })}
            type="checkbox"
            id="processingSplit"
            label="Сплит (да/нет)"
          />
        </Col>
      </Row>
    </Form>
  )
}

export const ProcessingModalForm = ({ isShow, onClose, selectedItem }) => {
  const [isView, setIsView] = useState(false)

  useEffect(() => {
    if (selectedItem) {
      setIsView(true)
    } else {
      setIsView(false)
    }
  }, [selectedItem])

  return (
    <Modal show={isShow} onHide={onClose} size="lg">
      <Modal.Header closeButton />
      <Modal.Body>
        <ModalForm selectedItem={selectedItem} isView={isView} onClose={onClose} />
      </Modal.Body>
      <Modal.Footer>
        <Button className={isView ? '' : 'visually-hidden'} onClick={() => setIsView(false)} type="button">
          Изменить
        </Button>
        <Button className={isView ? 'visually-hidden' : ''} type="submit" form="processingForm">
          Сохранить
        </Button>
        <Button onClick={onClose} type="button" variant="secondary">
          Закрыть
        </Button>
      </Modal.Footer>
    </Modal>
  )
}
